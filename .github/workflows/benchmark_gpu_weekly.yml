# Run CUDA benchmarks on multiple Hyperstack VMs and return parsed results to Slab CI bot.
name: Cuda weekly benchmarks

on:
  schedule:
    # Weekly benchmarks will be triggered each Saturday at 1a.m.
    - cron: '0 1 * * 6'


permissions: {}

jobs:
  run-benchmarks-1-h100:
    name: Run integer benchmarks (1xH100)
    if: github.repository == 'zama-ai/tfhe-rs'
    uses: ./.github/workflows/benchmark_gpu_common.yml
    with:
      profile: single-h100
      hardware_name: n3-H100x1
      command: integer,integer_multi_bit
      op_flavor: default
      bench_type: latency
      all_precisions: true
    secrets:
      BOT_USERNAME: ${{ secrets.BOT_USERNAME }}
      SLACK_CHANNEL: ${{ secrets.SLACK_CHANNEL }}
      SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
      REPO_CHECKOUT_TOKEN: ${{ secrets.REPO_CHECKOUT_TOKEN }}
      JOB_SECRET: ${{ secrets.JOB_SECRET }}
      SLAB_ACTION_TOKEN: ${{ secrets.SLAB_ACTION_TOKEN }}
      SLAB_URL: ${{ secrets.SLAB_URL }}
      SLAB_BASE_URL: ${{ secrets.SLAB_BASE_URL }}

  run-benchmarks-2-h100:
    name: Run integer benchmarks (2xH100)
    if: github.repository == 'zama-ai/tfhe-rs'
    uses: ./.github/workflows/benchmark_gpu_common.yml
    with:
      profile: 2-h100
      hardware_name: n3-H100x2
      command: integer_multi_bit
      op_flavor: default
      bench_type: latency
      all_precisions: true
    secrets:
      BOT_USERNAME: ${{ secrets.BOT_USERNAME }}
      SLACK_CHANNEL: ${{ secrets.SLACK_CHANNEL }}
      SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
      REPO_CHECKOUT_TOKEN: ${{ secrets.REPO_CHECKOUT_TOKEN }}
      JOB_SECRET: ${{ secrets.JOB_SECRET }}
      SLAB_ACTION_TOKEN: ${{ secrets.SLAB_ACTION_TOKEN }}
      SLAB_URL: ${{ secrets.SLAB_URL }}
      SLAB_BASE_URL: ${{ secrets.SLAB_BASE_URL }}

  run-benchmarks-8-h100:
    name: Run integer benchmarks (8xH100)
    if: github.repository == 'zama-ai/tfhe-rs'
    uses: ./.github/workflows/benchmark_gpu_common.yml
    with:
      profile: multi-h100
      hardware_name: n3-H100x8
      command: integer_multi_bit
      op_flavor: default
      bench_type: latency
      all_precisions: true
    secrets:
      BOT_USERNAME: ${{ secrets.BOT_USERNAME }}
      SLACK_CHANNEL: ${{ secrets.SLACK_CHANNEL }}
      SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
      REPO_CHECKOUT_TOKEN: ${{ secrets.REPO_CHECKOUT_TOKEN }}
      JOB_SECRET: ${{ secrets.JOB_SECRET }}
      SLAB_ACTION_TOKEN: ${{ secrets.SLAB_ACTION_TOKEN }}
      SLAB_URL: ${{ secrets.SLAB_URL }}
      SLAB_BASE_URL: ${{ secrets.SLAB_BASE_URL }}

  run-benchmarks-l40:
    name: Run integer benchmarks (L40)
    if: github.repository == 'zama-ai/tfhe-rs'
    uses: ./.github/workflows/benchmark_gpu_common.yml
    with:
      profile: l40
      hardware_name: n3-L40x1
      command: integer_multi_bit,integer_compression,pbs,ks
      op_flavor: default
      bench_type: latency
      all_precisions: true
    secrets:
      BOT_USERNAME: ${{ secrets.BOT_USERNAME }}
      SLACK_CHANNEL: ${{ secrets.SLACK_CHANNEL }}
      SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
      REPO_CHECKOUT_TOKEN: ${{ secrets.REPO_CHECKOUT_TOKEN }}
      JOB_SECRET: ${{ secrets.JOB_SECRET }}
      SLAB_ACTION_TOKEN: ${{ secrets.SLAB_ACTION_TOKEN }}
      SLAB_URL: ${{ secrets.SLAB_URL }}
      SLAB_BASE_URL: ${{ secrets.SLAB_BASE_URL }}

  run-benchmarks-1-h100-core-crypto:
    name: Run core-crypto benchmarks (1xH100)
    if: github.repository == 'zama-ai/tfhe-rs'
    uses: ./.github/workflows/benchmark_gpu_common.yml
    with:
      profile: single-h100
      hardware_name: n3-H100x1
      command: pbs,pbs128,ks,ks_pbs
      bench_type: latency
    secrets:
      BOT_USERNAME: ${{ secrets.BOT_USERNAME }}
      SLACK_CHANNEL: ${{ secrets.SLACK_CHANNEL }}
      SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
      REPO_CHECKOUT_TOKEN: ${{ secrets.REPO_CHECKOUT_TOKEN }}
      JOB_SECRET: ${{ secrets.JOB_SECRET }}
      SLAB_ACTION_TOKEN: ${{ secrets.SLAB_ACTION_TOKEN }}
      SLAB_URL: ${{ secrets.SLAB_URL }}
      SLAB_BASE_URL: ${{ secrets.SLAB_BASE_URL }}
