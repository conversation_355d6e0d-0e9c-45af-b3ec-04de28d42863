# Publish new release of tfhe-rs on various platform.
name: Publish release

on:
  workflow_dispatch:
    inputs:
      dry_run:
        description: "Dry-run"
        type: boolean
        default: true
      push_to_crates:
        description: "Push to crate"
        type: boolean
        default: true
      push_web_package:
        description: "Push web js package"
        type: boolean
        default: true
      push_node_package:
        description: "Push node js package"
        type: boolean
        default: true
      npm_latest_tag:
        description: "Set NPM tag as latest"
        type: boolean
        default: false

env:
  ACTION_RUN_URL: ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}
  NPM_TAG: ""
  SLACK_CHANNEL: ${{ secrets.SLACK_CHANNEL }}
  SLACK_ICON: https://pbs.twimg.com/profile_images/1274014582265298945/OjBKP9kn_400x400.png
  SLACK_USERNAME: ${{ secrets.BOT_USERNAME }}
  SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}

permissions: {}

jobs:
  verify_tag:
    uses: ./.github/workflows/verify_tagged_commit.yml
    secrets:
      RELEASE_TEAM: ${{ secrets.RELEASE_TEAM }}
      READ_ORG_TOKEN: ${{ secrets.READ_ORG_TOKEN }}

  package:
    runs-on: ubuntu-latest
    needs: verify_tag
    outputs:
      hash: ${{ steps.hash.outputs.hash }}
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          fetch-depth: 0
          persist-credentials: 'false'
          token: ${{ secrets.REPO_CHECKOUT_TOKEN }}
      - name: Prepare package
        run: |
          cargo package -p tfhe
      - uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        with:
          name: crate
          path: target/package/*.crate
      - name: generate hash
        id: hash
        run: cd target/package && echo "hash=$(sha256sum ./*.crate | base64 -w0)" >> "${GITHUB_OUTPUT}"

  provenance:
    if: ${{ !inputs.dry_run  }}
    needs: [package]
    uses: slsa-framework/slsa-github-generator/.github/workflows/generator_generic_slsa3.yml@v2.1.0
    permissions:
      # Needed to detect the GitHub Actions environment
      actions: read
      # Needed to create the provenance via GitHub OIDC
      id-token: write
      # Needed to upload assets/artifacts
      contents: write
    with:
      # SHA-256 hashes of the Crate package.
      base64-subjects: ${{ needs.package.outputs.hash }}

  publish_release:
    name: Publish Release
    needs: [package] # for comparing hashes
    runs-on: ubuntu-latest
    # For provenance of npmjs publish
    permissions:
      contents: read
      id-token: write
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          fetch-depth: 0
          persist-credentials: 'false'
          token: ${{ secrets.REPO_CHECKOUT_TOKEN }}
      - name: Create NPM version tag
        if: ${{ inputs.npm_latest_tag }}
        run: |
          echo "NPM_TAG=latest" >> "${GITHUB_ENV}"
      - name: Download artifact
        uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093 # v4.3.0
        with:
          name: crate
          path: target/package
      - name: Publish crate.io package
        if: ${{ inputs.push_to_crates }}
        env:
          CRATES_TOKEN: ${{ secrets.CARGO_REGISTRY_TOKEN }}
          DRY_RUN: ${{ inputs.dry_run && '--dry-run' || '' }}
        run: |
          # DRY_RUN expansion cannot be double quoted when variable contains empty string otherwise cargo publish 
          # would fail. This is safe since DRY_RUN is handled in the env section above.
          # shellcheck disable=SC2086
          cargo publish -p tfhe --token "${CRATES_TOKEN}" ${DRY_RUN}

      - name: Generate hash
        id: published_hash
        run: cd target/package && echo "pub_hash=$(sha256sum ./*.crate | base64 -w0)" >> "${GITHUB_OUTPUT}"

      - name: Slack notification (hashes comparison)
        if: ${{ needs.package.outputs.hash != steps.published_hash.outputs.pub_hash }}
        continue-on-error: true
        uses: rtCamp/action-slack-notify@e31e87e03dd19038e411e38ae27cbad084a90661 # v2.3.3
        env:
          SLACK_COLOR: failure
          SLACK_MESSAGE: "SLSA tfhe crate - hash comparison failure: (${{ env.ACTION_RUN_URL }})"

      - name: Build web package
        if: ${{ inputs.push_web_package }}
        run: |
          make build_web_js_api_parallel

      - name: Publish web package
        if: ${{ inputs.push_web_package }}
        uses: JS-DevTools/npm-publish@19c28f1ef146469e409470805ea4279d47c3d35c
        with:
          token: ${{ secrets.NPM_TOKEN }}
          package: tfhe/pkg/package.json
          dry-run: ${{ inputs.dry_run }}
          tag: ${{ env.NPM_TAG }}
          provenance: true

      - name: Build Node package
        if: ${{ inputs.push_node_package }}
        run: |
          rm -rf tfhe/pkg

          make build_node_js_api
          sed -i 's/"tfhe"/"node-tfhe"/g' tfhe/pkg/package.json

      - name: Publish Node package
        if: ${{ inputs.push_node_package }}
        uses: JS-DevTools/npm-publish@19c28f1ef146469e409470805ea4279d47c3d35c
        with:
          token: ${{ secrets.NPM_TOKEN }}
          package: tfhe/pkg/package.json
          dry-run: ${{ inputs.dry_run }}
          tag: ${{ env.NPM_TAG }}
          provenance: true

      - name: Slack Notification
        if: ${{ failure() || (cancelled() && github.event_name != 'pull_request') }}
        continue-on-error: true
        uses: rtCamp/action-slack-notify@e31e87e03dd19038e411e38ae27cbad084a90661 # v2.3.3
        env:
          SLACK_COLOR: ${{ job.status }}
          SLACK_MESSAGE: "tfhe release failed: (${{ env.ACTION_RUN_URL }})"
