# Test tfhe-fft
name: Cargo Test tfhe-fft

on:
  pull_request:
  push:
    branches:
      - main

env:
  CARGO_TERM_COLOR: always
  IS_PULL_REQUEST: ${{ github.event_name == 'pull_request' }}
  CHECKOUT_TOKEN: ${{ secrets.REPO_CHECKOUT_TOKEN || secrets.GITHUB_TOKEN }}

concurrency:
  group: ${{ github.workflow }}-${{ github.head_ref }}${{ github.ref == 'refs/heads/main' && github.sha || '' }}
  cancel-in-progress: true

permissions:
  contents: read

jobs:
  should-run:
    runs-on: ubuntu-latest
    permissions:
      pull-requests: read
    outputs:
      fft_test: ${{ env.IS_PULL_REQUEST == 'false' || steps.changed-files.outputs.fft_any_changed }}
    steps:
      - name: Checkout tfhe-rs
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
        with:
          fetch-depth: 0
          persist-credentials: 'false'
          token: ${{ env.CHECKOUT_TOKEN }}

      - name: Check for file changes
        id: changed-files
        uses: tj-actions/changed-files@ed68ef82c095e0d48ec87eccea555d944a631a4c # v46.0.5
        with:
          files_yaml: |
            fft:
              - tfhe/Cargo.toml
              - Makefile
              - tfhe-fft/**
              - '.github/workflows/cargo_test_fft.yml'

  cargo-tests-fft:
    needs: should-run
    if: needs.should-run.outputs.fft_test == 'true'
    runs-on: ${{ matrix.runner_type }}
    strategy:
      matrix:
        runner_type: [ ubuntu-latest, macos-latest, windows-latest ]
      fail-fast: false
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
        with:
          persist-credentials: 'false'
          token: ${{ env.CHECKOUT_TOKEN }}

      - name: Install Rust
        uses: actions-rs/toolchain@16499b5e05bf2e26879000db0c1d13f7e13fa3af
        with:
          toolchain: stable
          override: true

      - name: Test debug
        run: |
          make test_fft

      - name: Test serialization
        run: make test_fft_serde

      - name: Test no-std
        run: |
          make test_fft_no_std

  cargo-tests-fft-nightly:
    needs: should-run
    if: needs.should-run.outputs.fft_test == 'true'
    runs-on: ${{ matrix.runner_type }}
    strategy:
      matrix:
        runner_type: [ ubuntu-latest, macos-latest, windows-latest ]
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
        with:
          persist-credentials: 'false'
          token: ${{ env.CHECKOUT_TOKEN }}

      - name: Install Rust
        uses: actions-rs/toolchain@16499b5e05bf2e26879000db0c1d13f7e13fa3af
        with:
          toolchain: nightly
          override: true

      - name: Test nightly
        run: |
          make test_fft_nightly

      - name: Test no-std nightly
        run: |
          make test_fft_no_std_nightly

  cargo-tests-fft-node-js:
    needs: should-run
    if: needs.should-run.outputs.fft_test == 'true'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
        with:
          persist-credentials: 'false'
          token: ${{ env.CHECKOUT_TOKEN }}

      - name: Test node js
        run: |
          make install_node
          make test_fft_node_js_ci

  cargo-tests-fft-successful:
    needs: [ should-run, cargo-tests-fft, cargo-tests-fft-nightly, cargo-tests-fft-node-js ]
    if: ${{ always() }}
    runs-on: ubuntu-latest
    steps:
      - name: Tests do not need to run
        if: needs.should-run.outputs.fft_test == 'false'
        run: |
          echo "tfhe-fft files haven't changed tests don't need to run"

      - name: Check all tests passed
        if: needs.should-run.outputs.fft_test == 'true' &&
          needs.cargo-tests-fft.result == 'success' &&
          needs.cargo-tests-fft-nightly.result == 'success' &&
          needs.cargo-tests-fft-node-js.result == 'success'
        run: |
          echo "All tfhe-fft test passed"

      - name: Check tests failure
        if: needs.should-run.outputs.fft_test == 'true' &&
          (needs.cargo-tests-fft.result != 'success' ||
          needs.cargo-tests-fft-nightly.result != 'success' ||
          needs.cargo-tests-fft-node-js.result != 'success')
        run: |
          echo "Some tfhe-fft tests failed"
          exit 1
