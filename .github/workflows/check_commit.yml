# Check commit and PR compliance
name: Check commit and PR compliance
on:
  pull_request:

permissions: {}

jobs:
  check-commit-pr:
    name: Check commit and PR
    runs-on: ubuntu-latest
    permissions:
      contents: read
      pull-requests: write # Permission needed to scan commits in a pull-request and write issue comment
    steps:
      - name: Check first line
        uses: gsactions/commit-message-checker@16fa2d5de096ae0d35626443bcd24f1e756cafee
        with:
          pattern: '^((feat|fix|chore|refactor|style|test|docs|doc)(\([\w\-_]+\))?\!?\:) .+$'
          flags: "gs"
          error: 'Your first line has to contain a commit type and scope like "feat(my_feature): msg".'
          excludeDescription: "true" # optional: this excludes the description body of a pull request
          excludeTitle: "true" # optional: this excludes the title of a pull request
          checkAllCommitMessages: "true" # optional: this checks all commits associated with a pull request
          accessToken: ${{ secrets.GITHUB_TOKEN }} # github access token is only required if checkAllCommitMessages is true

      - name: Check line length
        uses: gsactions/commit-message-checker@16fa2d5de096ae0d35626443bcd24f1e756cafee
        with:
          pattern: '(^.{0,74}$\r?\n?){0,20}'
          flags: "gm"
          error: "The maximum line length of 74 characters is exceeded."
          excludeDescription: "true" # optional: this excludes the description body of a pull request
          excludeTitle: "true" # optional: this excludes the title of a pull request
          checkAllCommitMessages: "true" # optional: this checks all commits associated with a pull request
          accessToken: ${{ secrets.GITHUB_TOKEN }} # github access token is only required if checkAllCommitMessages is true

      - name: Check commit signatures
        uses: 1Password/check-signed-commits-action@ed2885f3ed2577a4f5d3c3fe895432a557d23d52
