# Verify a tagged commit
name: Verify tagged commit

on:
  workflow_call:
    secrets:
      RELEASE_TEAM:
        required: true
      READ_ORG_TOKEN:
        required: true

permissions: {}

jobs:
  checks:
    runs-on: ubuntu-latest
    if: startsWith(github.ref, 'refs/tags/')
    steps:
      # Check triggering actor membership
      - name: Actor verification
        id: actor_check
        uses: morfien101/actions-authorized-user@4a3cfbf0bcb3cafe4a71710a278920c5d94bb38b
        with:
          username: ${{ github.triggering_actor }}
          org: ${{ github.repository_owner }}
          team: ${{ secrets.RELEASE_TEAM }}
          github_token: ${{ secrets.READ_ORG_TOKEN }}

      - name: Actor authorized
        run: |
          if [ "${ACTOR_CHECK_OUTPUT}" == "false" ]; then
            echo "Actor '${TRIGGERING_ACTOR}' is not authorized to perform release"
            exit 1
          fi
        env:
          TRIGGERING_ACTOR: ${{ github.triggering_actor }}
          ACTOR_CHECK_OUTPUT: ${{ steps.actor_check.outputs.authorized }}
