name: 'Close unverified PRs'
on:
  schedule:
    - cron: '30 1 * * *'

permissions: {}

jobs:
  stale:
    runs-on: ubuntu-latest
    permissions:
      issues: read
      pull-requests: write
    steps:
      - uses: actions/stale@5bef64f19d7facfb25b37b414482c7164d639639 # v9.1.0
        with:
          stale-pr-message: 'This PR is unverified and has been open for 2 days, it will now be closed. If you want to contribute please sign the CLA as indicated by the bot.'
          days-before-stale: 2
          days-before-close: 0
          # We are not interested in suppressing issues so have a currently non existent label
          # if we ever accept issues to become stale/closable this label will be the signal for that
          only-issue-labels: can-be-auto-closed
          # Only unverified PRs are an issue
          exempt-pr-labels: cla-signed
          # We don't want people commenting to keep an unverified PR
          ignore-updates: true
