# Compile and test tfhe-cuda-backend on an RTX 4090 machine
name: <PERSON>uda - 4090 full tests

env:
  CARGO_TERM_COLOR: always
  ACTION_RUN_URL: ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}
  RUSTFLAGS: "-C target-cpu=native"
  RUST_BACKTRACE: "full"
  RUST_MIN_STACK: "8388608"
  SLACK_CHANNEL: ${{ secrets.SLACK_CHANNEL }}
  SLACK_ICON: https://pbs.twimg.com/profile_images/1274014582265298945/OjBKP9kn_400x400.png
  SLACK_USERNAME: ${{ secrets.BOT_USERNAME }}
  SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
  CHECKOUT_TOKEN: ${{ secrets.REPO_CHECKOUT_TOKEN || secrets.GITHUB_TOKEN }}

on:
  # Allows you to run this workflow manually from the Actions tab as an alternative.
  workflow_dispatch:
  pull_request:
    types: [ labeled ]
  schedule:
    # Nightly tests @ 1AM after each work day
    - cron: "0 1 * * MON-FRI"

permissions:
  contents: read

jobs:
  cuda-tests-linux:
    name: CUDA tests (RTX 4090)
    if: github.event_name == 'workflow_dispatch' ||
      contains(github.event.label.name, '4090_test') ||
      (github.event_name == 'schedule' &&  github.repository == 'zama-ai/tfhe-rs')
    concurrency:
      group: ${{ github.workflow_ref }}
      cancel-in-progress: true
    runs-on: ["self-hosted", "4090-desktop"]
    timeout-minutes: 1440 # 24 hours

    steps:
      - name: Checkout tfhe-rs
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
        with:
          persist-credentials: 'false'
          token: ${{ env.CHECKOUT_TOKEN }}

      - name: Install latest stable
        uses: dtolnay/rust-toolchain@b3b07ba8b418998c39fb20f53e8b695cdcc8de1b # zizmor: ignore[stale-action-refs] this action doesn't create releases
        with:
          toolchain: stable

      - name: Run fmt checks
        run: |
          make check_fmt_gpu

      - name: Run clippy checks
        run: |
          make pcc_gpu

      - name: Run core crypto, integer and internal CUDA backend tests
        run: |
          make test_gpu

      - name: Run user docs tests
        run: |
          make test_user_doc_gpu

      - name: Test C API
        run: |
          make test_c_api_gpu

      - name: Run High Level API Tests
        run: |
          make test_high_level_api_gpu

      - uses: actions-ecosystem/action-remove-labels@2ce5d41b4b6aa8503e285553f75ed56e0a40bae0
        if: ${{ always() && github.event_name == 'pull_request' }}
        with:
          labels: 4090_test
          github_token: ${{ secrets.GITHUB_TOKEN }}

      - name: Slack Notification
        if: ${{ failure() || (cancelled() && github.event_name != 'pull_request') }}
        continue-on-error: true
        uses: rtCamp/action-slack-notify@e31e87e03dd19038e411e38ae27cbad084a90661
        env:
          SLACK_COLOR: ${{ job.status }}
          SLACK_MESSAGE: "CUDA RTX 4090 tests finished with status: ${{ job.status }}. (${{ env.ACTION_RUN_URL }})"
