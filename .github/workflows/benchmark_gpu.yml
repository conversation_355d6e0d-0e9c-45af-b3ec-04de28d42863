# Run CUDA benchmarks on a Hyperstack VM and return parsed results to Slab CI bot.
name: Cuda benchmarks

on:
  workflow_dispatch:
    inputs:
      profile:
        description: "Instance type"
        required: true
        type: choice
        options:
          - "l40 (n3-L40x1)"
          - "4-l40 (n3-L40x4)"
          - "multi-a100-nvlink (n3-A100x8-NVLink)"
          - "single-h100 (n3-H100x1)"
          - "2-h100 (n3-H100x2)"
          - "4-h100 (n3-H100x4)"
          - "multi-h100 (n3-H100x8)"
          - "multi-h100-nvlink (n3-H100x8-NVLink)"
          - "multi-h100-sxm5 (n3-H100x8-SXM5)"
      command:
        description: "Benchmark command to run"
        type: choice
        default: integer_multi_bit
        options:
          - integer
          - integer_multi_bit
          - integer_compression
          - pbs
          - pbs128
          - ks
          - ks_pbs
          - integer_zk
      op_flavor:
        description: "Operations set to run"
        type: choice
        default: default
        options:
          - default
          - fast_default
          - unchecked
      all_precisions:
        description: "Run all precisions"
        type: boolean
        default: false
      bench_type:
        description: "Benchmarks type"
        type: choice
        default: latency
        options:
          - latency
          - throughput
          - both
      params_type:
        description: "Parameters type"
        type: choice
        default: multi_bit
        options:
          - classical
          - multi_bit
          - both


permissions: {}

jobs:
  parse-inputs:
    runs-on: ubuntu-latest
    outputs:
      profile: ${{ steps.parse_profile.outputs.profile }}
      hardware_name: ${{ steps.parse_hardware_name.outputs.name }}
    env:
      INPUTS_PROFILE: ${{ inputs.profile }}
    steps:
      - name: Parse profile
        id: parse_profile
        run: |
          # Use Sed to extract a value from a string, this cannot be done with the ${variable//search/replace} pattern.
          # shellcheck disable=SC2001
          PROFILE=$(echo "${INPUTS_PROFILE}" | sed 's|\(.*\)[[:space:]](.*)|\1|')
          echo "profile=${PROFILE}" >> "${GITHUB_OUTPUT}"

      - name: Parse hardware name
        id: parse_hardware_name
        run: |
          # Use Sed to extract a value from a string, this cannot be done with the ${variable//search/replace} pattern.
          # shellcheck disable=SC2001
          NAME=$(echo "${INPUTS_PROFILE}" | sed 's|.*[[:space:]](\(.*\))|\1|')
          echo "name=${NAME}" >> "${GITHUB_OUTPUT}"

  run-benchmarks:
    name: Run benchmarks
    needs: parse-inputs
    uses: ./.github/workflows/benchmark_gpu_common.yml
    with:
      profile: ${{ needs.parse-inputs.outputs.profile }}
      hardware_name: ${{ needs.parse-inputs.outputs.hardware_name }}
      command: ${{ inputs.command }}
      op_flavor: ${{ inputs.op_flavor }}
      bench_type: ${{ inputs.bench_type }}
      params_type: ${{ inputs.params_type }}
      all_precisions: ${{ inputs.all_precisions }}
    secrets:
      BOT_USERNAME: ${{ secrets.BOT_USERNAME }}
      SLACK_CHANNEL: ${{ secrets.SLACK_CHANNEL }}
      SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
      REPO_CHECKOUT_TOKEN: ${{ secrets.REPO_CHECKOUT_TOKEN }}
      JOB_SECRET: ${{ secrets.JOB_SECRET }}
      SLAB_ACTION_TOKEN: ${{ secrets.SLAB_ACTION_TOKEN }}
      SLAB_URL: ${{ secrets.SLAB_URL }}
      SLAB_BASE_URL: ${{ secrets.SLAB_BASE_URL }}
