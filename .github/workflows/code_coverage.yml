name: Code Coverage

env:
  CARGO_TERM_COLOR: always
  ACTION_RUN_URL: ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}
  RUSTFLAGS: "-C target-cpu=native"
  RUST_BACKTRACE: "full"
  RUST_MIN_STACK: "8388608"
  SLACK_CHANNEL: ${{ secrets.SLACK_CHANNEL }}
  SLACK_ICON: https://pbs.twimg.com/profile_images/1274014582265298945/OjBKP9kn_400x400.png
  SLACK_USERNAME: ${{ secrets.BOT_USERNAME }}
  SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
  CHECKOUT_TOKEN: ${{ secrets.REPO_CHECKOUT_TOKEN || secrets.GITHUB_TOKEN }}

on:
  # Allows you to run this workflow manually from the Actions tab as an alternative.
  workflow_dispatch:
  # Code coverage workflow is only run via workflow_dispatch event since execution duration is not stabilized yet.

permissions:
  contents: read

jobs:
  setup-instance:
    name: Setup instance (code-coverage)
    runs-on: ubuntu-latest
    outputs:
      runner-name: ${{ steps.start-instance.outputs.label }}
    steps:
      - name: Start instance
        id: start-instance
        uses: zama-ai/slab-github-runner@79939325c3c429837c10d6041e4fd8589d328bac
        with:
          mode: start
          github-token: ${{ secrets.SLAB_ACTION_TOKEN }}
          slab-url: ${{ secrets.SLAB_BASE_URL }}
          job-secret: ${{ secrets.JOB_SECRET }}
          backend: aws
          profile: cpu-small

  code-coverage:
    name: Code coverage tests
    needs: setup-instance
    concurrency:
      group: ${{ github.workflow_ref }}_${{ github.event_name }}
      cancel-in-progress: true
    runs-on: ${{ needs.setup-instance.outputs.runner-name }}
    timeout-minutes: 5760 # 4 days
    steps:
      - name: Checkout tfhe-rs
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
        with:
          persist-credentials: 'false'
          token: ${{ env.CHECKOUT_TOKEN }}

      - name: Install latest stable
        uses: dtolnay/rust-toolchain@b3b07ba8b418998c39fb20f53e8b695cdcc8de1b # zizmor: ignore[stale-action-refs] this action doesn't create releases
        with:
          toolchain: stable

      - name: Check for file changes
        id: changed-files
        uses: tj-actions/changed-files@ed68ef82c095e0d48ec87eccea555d944a631a4c # v46.0.5
        with:
          files_yaml: |
            tfhe:
              - tfhe/src/**
            tfhe_csprng:
              - tfhe-csprng/src/**

      - name: Generate Keys
        if: steps.changed-files.outputs.tfhe_any_changed == 'true'
        run: |
          make GEN_KEY_CACHE_COVERAGE_ONLY=TRUE gen_key_cache
          make gen_key_cache_core_crypto

      - name: Run coverage for core_crypto
        if: steps.changed-files.outputs.tfhe_any_changed == 'true'
        run: |
          make test_core_crypto_cov AVX512_SUPPORT=ON

      - name: Run coverage for boolean
        if: steps.changed-files.outputs.tfhe_any_changed == 'true'
        run: |
          make test_boolean_cov

      - name: Run coverage for shortint
        if: steps.changed-files.outputs.tfhe_any_changed == 'true'
        run: |
          make test_shortint_cov

      - name: Upload tfhe coverage to Codecov
        uses: codecov/codecov-action@18283e04ce6e62d37312384ff67231eb8fd56d24
        if: steps.changed-files.outputs.tfhe_any_changed == 'true'
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
          directory: ./coverage/
          fail_ci_if_error: true
          files: shortint/cobertura.xml,boolean/cobertura.xml,core_crypto/cobertura.xml,core_crypto_avx512/cobertura.xml

      - name: Run integer coverage
        if: steps.changed-files.outputs.tfhe_any_changed == 'true'
        run: |
          make test_integer_cov

      - name: Upload tfhe coverage to Codecov
        uses: codecov/codecov-action@18283e04ce6e62d37312384ff67231eb8fd56d24
        if: steps.changed-files.outputs.tfhe_any_changed == 'true'
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
          directory: ./coverage/
          fail_ci_if_error: true
          files: integer/cobertura.xml

      - name: Slack Notification
        if: ${{ failure() || (cancelled() && github.event_name != 'pull_request') }}
        continue-on-error: true
        uses: rtCamp/action-slack-notify@e31e87e03dd19038e411e38ae27cbad084a90661
        env:
          SLACK_COLOR: ${{ job.status }}
          SLACK_MESSAGE: "Code coverage finished with status: ${{ job.status }}. (${{ env.ACTION_RUN_URL }})"

  teardown-instance:
    name: Teardown instance (code-coverage)
    if: ${{ always() && needs.setup-instance.result == 'success' }}
    needs: [ setup-instance, code-coverage ]
    runs-on: ubuntu-latest
    steps:
      - name: Stop instance
        id: stop-instance
        uses: zama-ai/slab-github-runner@79939325c3c429837c10d6041e4fd8589d328bac
        with:
          mode: stop
          github-token: ${{ secrets.SLAB_ACTION_TOKEN }}
          slab-url: ${{ secrets.SLAB_BASE_URL }}
          job-secret: ${{ secrets.JOB_SECRET }}
          label: ${{ needs.setup-instance.outputs.runner-name }}

      - name: Slack Notification
        if: ${{ failure() }}
        continue-on-error: true
        uses: rtCamp/action-slack-notify@e31e87e03dd19038e411e38ae27cbad084a90661
        env:
          SLACK_COLOR: ${{ job.status }}
          SLACK_MESSAGE: "Instance teardown (code-coverage) finished with status: ${{ job.status }}. (${{ env.ACTION_RUN_URL }})"
