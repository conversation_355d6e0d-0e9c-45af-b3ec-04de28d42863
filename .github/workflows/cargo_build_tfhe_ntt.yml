# Build tfhe-ntt
name: Cargo Build tfhe-ntt

on:
  pull_request:

env:
  CARGO_TERM_COLOR: always
  CHECKOUT_TOKEN: ${{ secrets.REPO_CHECKOUT_TOKEN || secrets.GITHUB_TOKEN }}

concurrency:
  group: ${{ github.workflow }}-${{ github.head_ref }}
  cancel-in-progress: true

permissions:
  contents: read

jobs:
  cargo-builds-ntt:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ubuntu-latest, macos-latest, windows-latest]
      fail-fast: false
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
        with:
          persist-credentials: 'false'
          token: ${{ env.CHECKOUT_TOKEN }}

      - name: Install Rust
        uses: actions-rs/toolchain@16499b5e05bf2e26879000db0c1d13f7e13fa3af
        with:
          toolchain: stable
          override: true

      - name: Run pcc checks
        run: |
          make pcc_ntt

      - name: Build release
        run: |
          make build_ntt

      - name: Build release no-std
        run: |
          make build_ntt_no_std
