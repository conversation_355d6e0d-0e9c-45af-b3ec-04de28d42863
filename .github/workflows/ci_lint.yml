# Lint and check CI
name: <PERSON><PERSON> and Checks

on:
  pull_request:

env:
  ACTIONLINT_VERSION: 1.7.7
  ACTIONLINT_CHECKSUM: "023070a287cd8cccd71515fedc843f1985bf96c436b7effaecce67290e7e0757"
  CHECKOUT_TOKEN: ${{ secrets.REPO_CHECKOUT_TOKEN || secrets.GITHUB_TOKEN }}

permissions:
  contents: read

jobs:
  lint-check:
    name: <PERSON><PERSON> and checks
    runs-on: ubuntu-latest
    steps:
      - name: Checkout tfhe-rs
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
        with:
          persist-credentials: 'false'
          token: ${{ env.CHECKOUT_TOKEN }}

      - name: Get actionlint
        run: |
          wget "https://github.com/rhysd/actionlint/releases/download/v${ACTIONLINT_VERSION}/actionlint_${ACTIONLINT_VERSION}_linux_amd64.tar.gz"
          echo "${ACTIONLINT_CHECKSUM} actionlint_${ACTIONLINT_VERSION}_linux_amd64.tar.gz" > checksum
          sha256sum -c checksum
          tar -xf actionlint_"${ACTIONLINT_VERSION}"_linux_amd64.tar.gz actionlint
          ln -s "$(pwd)/actionlint" /usr/local/bin/

      - name: Lint workflows
        run: |
          make lint_workflow

      - name: Check workflows security
        run: |
          make check_workflow_security
        env:
          GH_TOKEN: ${{ env.CHECKOUT_TOKEN }}

      - name: Ensure SHA pinned actions
        uses: zgosalvez/github-actions-ensure-sha-pinned-actions@fc87bb5b5a97953d987372e74478de634726b3e5 # v3.0.25
        with:
          allowlist: |
            slsa-framework/slsa-github-generator
            ./
