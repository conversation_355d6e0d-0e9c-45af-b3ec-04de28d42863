# Run WASM client benchmarks on an instance and return parsed results to Slab CI bot.
name: WASM client benchmarks

on:
  workflow_dispatch:
  push:
    branches:
      - main
  schedule:
    # Weekly benchmarks will be triggered each Saturday at 1a.m.
    - cron: '0 1 * * 6'

env:
  CARGO_TERM_COLOR: always
  RESULTS_FILENAME: parsed_benchmark_results_${{ github.sha }}.json
  ACTION_RUN_URL: ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}
  RUST_BACKTRACE: "full"
  RUST_MIN_STACK: "8388608"
  SLACK_CHANNEL: ${{ secrets.SLACK_CHANNEL }}
  SLACK_ICON: https://pbs.twimg.com/profile_images/1274014582265298945/OjBKP9kn_400x400.png
  SLACK_USERNAME: ${{ secrets.BOT_USERNAME }}
  SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}


permissions: {}

jobs:
  should-run:
    runs-on: ubuntu-latest
    if: github.event_name == 'workflow_dispatch' ||
      (github.event_name == 'schedule' && github.repository == 'zama-ai/tfhe-rs') ||
      (github.event_name == 'push' && github.repository == 'zama-ai/tfhe-rs')
    permissions:
      pull-requests: read
    outputs:
      wasm_bench: ${{ steps.changed-files.outputs.wasm_bench_any_changed }}
    steps:
      - name: Checkout tfhe-rs
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
        with:
          fetch-depth: 0
          persist-credentials: 'false'
          token: ${{ secrets.REPO_CHECKOUT_TOKEN }}

      - name: Check for file changes
        id: changed-files
        uses: tj-actions/changed-files@ed68ef82c095e0d48ec87eccea555d944a631a4c # v46.0.5
        with:
          files_yaml: |
            wasm_bench:
              - tfhe/Cargo.toml
              - tfhe-csprng/**
              - tfhe-zk-pok/**
              - tfhe/src/**
              - '!tfhe/src/c_api/**'
              - tfhe/web_wasm_parallel_tests/**
              - .github/workflows/wasm_client_benchmark.yml

  setup-instance:
    name: Setup instance (wasm-client-benchmarks)
    if: github.event_name == 'workflow_dispatch' ||
      (github.event_name == 'schedule' && github.repository == 'zama-ai/tfhe-rs') ||
      (github.event_name == 'push' && github.repository == 'zama-ai/tfhe-rs' && needs.should-run.outputs.wasm_bench)
    needs: should-run
    runs-on: ubuntu-latest
    outputs:
      runner-name: ${{ steps.start-instance.outputs.label }}
    steps:
      - name: Start instance
        id: start-instance
        uses: zama-ai/slab-github-runner@79939325c3c429837c10d6041e4fd8589d328bac
        with:
          mode: start
          github-token: ${{ secrets.SLAB_ACTION_TOKEN }}
          slab-url: ${{ secrets.SLAB_BASE_URL }}
          job-secret: ${{ secrets.JOB_SECRET }}
          backend: aws
          profile: cpu-small

  wasm-client-benchmarks:
    name: Execute WASM client benchmarks
    needs: setup-instance
    if: needs.setup-instance.result != 'skipped'
    runs-on: ${{ needs.setup-instance.outputs.runner-name }}
    strategy:
      max-parallel: 1
      matrix:
        browser: [ chrome, firefox ]
    steps:
      - name: Checkout tfhe-rs repo with tags
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
        with:
          fetch-depth: 0
          persist-credentials: 'false'
          token: ${{ secrets.REPO_CHECKOUT_TOKEN }}

      - name: Get benchmark details
        run: |
          COMMIT_DATE=$(git --no-pager show -s --format=%cd --date=iso8601-strict "${SHA}");
          {
            echo "BENCH_DATE=$(date --iso-8601=seconds)";
            echo "COMMIT_DATE=${COMMIT_DATE}";
            echo "COMMIT_HASH=$(git describe --tags --dirty)";
          } >> "${GITHUB_ENV}"
        env:
          SHA: ${{ github.sha }}

      - name: Install rust
        uses: dtolnay/rust-toolchain@b3b07ba8b418998c39fb20f53e8b695cdcc8de1b # zizmor: ignore[stale-action-refs] this action doesn't create releases
        with:
          toolchain: nightly

      - name: Get Node version
        run: |
          echo "NODE_VERSION=$(make node_version)" >> "${GITHUB_ENV}"

      - name: Node cache restoration
        id: node-cache
        uses: actions/cache/restore@5a3ec84eff668545956fd18022155c47e93e2684 #v4.2.3
        with:
          path: |
            ~/.nvm
            ~/.npm
          key: node-${{ env.NODE_VERSION }}

      - name: Install Node
        if: steps.node-cache.outputs.cache-hit != 'true'
        run: |
          make install_node

      - name: Node cache save
        uses: actions/cache/save@5a3ec84eff668545956fd18022155c47e93e2684 #v4.2.3
        if: steps.node-cache.outputs.cache-hit != 'true'
        with:
          path: |
            ~/.nvm
            ~/.npm
          key: node-${{ env.NODE_VERSION }}

      - name: Install web resources
        run: |
          make install_"${BROWSER}"_browser
          make install_"${BROWSER}"_web_driver
        env:
          BROWSER: ${{ matrix.browser }}

      - name: Run benchmarks
        run: |
          make bench_web_js_api_parallel_"${BROWSER}"_ci
        env:
          BROWSER: ${{ matrix.browser }}

      - name: Parse results
        run: |
          make parse_wasm_benchmarks
          python3 ./ci/benchmark_parser.py tfhe-benchmark/wasm_pk_gen.csv "${RESULTS_FILENAME}" \
          --database tfhe_rs \
          --hardware "m6i.4xlarge" \
          --project-version "${COMMIT_HASH}" \
          --branch "${REF_NAME}" \
          --commit-date "${COMMIT_DATE}" \
          --bench-date "${BENCH_DATE}" \
          --key-gen
          rm tfhe-benchmark/wasm_pk_gen.csv
        env:
          REF_NAME: ${{ github.ref_name }}

      # Run these benchmarks only once
      - name: Measure public key and ciphertext sizes in HL Api
        if:  matrix.browser == 'chrome'
        run: |
          make measure_hlapi_compact_pk_ct_sizes

      - name: Parse key and ciphertext sizes results
        if:  matrix.browser == 'chrome'
        run: |
          python3 ./ci/benchmark_parser.py tfhe-benchmark/hlapi_cpk_and_cctl_sizes.csv "${RESULTS_FILENAME}" \
          --key-gen \
          --append-results

      - name: Upload parsed results artifact
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02
        with:
          name: ${{ github.sha }}_wasm_${{ matrix.browser }}
          path: ${{ env.RESULTS_FILENAME }}

      - name: Checkout Slab repo
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
        with:
          repository: zama-ai/slab
          path: slab
          persist-credentials: 'false'
          token: ${{ secrets.REPO_CHECKOUT_TOKEN }}

      - name: Send data to Slab
        shell: bash
        run: |
          python3 slab/scripts/data_sender.py "${RESULTS_FILENAME}" "${JOB_SECRET}" \
          --slab-url "${SLAB_URL}"
        env:
          JOB_SECRET: ${{ secrets.JOB_SECRET }}
          SLAB_URL: ${{ secrets.SLAB_URL }}

      - name: Slack Notification
        if: ${{ failure() || (cancelled() && github.event_name != 'pull_request') }}
        continue-on-error: true
        uses: rtCamp/action-slack-notify@e31e87e03dd19038e411e38ae27cbad084a90661
        env:
          SLACK_COLOR: ${{ job.status }}
          SLACK_MESSAGE: "WASM benchmarks (${{ matrix.browser }}) finished with status: ${{ job.status }}. (${{ env.ACTION_RUN_URL }})"

  teardown-instance:
    name: Teardown instance (wasm-client-benchmarks)
    if: ${{ always() && needs.setup-instance.result == 'success' }}
    needs: [ setup-instance, wasm-client-benchmarks ]
    runs-on: ubuntu-latest
    steps:
      - name: Stop instance
        id: stop-instance
        uses: zama-ai/slab-github-runner@79939325c3c429837c10d6041e4fd8589d328bac
        with:
          mode: stop
          github-token: ${{ secrets.SLAB_ACTION_TOKEN }}
          slab-url: ${{ secrets.SLAB_BASE_URL }}
          job-secret: ${{ secrets.JOB_SECRET }}
          label: ${{ needs.setup-instance.outputs.runner-name }}

      - name: Slack Notification
        if: ${{ failure() }}
        continue-on-error: true
        uses: rtCamp/action-slack-notify@e31e87e03dd19038e411e38ae27cbad084a90661
        env:
          SLACK_COLOR: ${{ job.status }}
          SLACK_MESSAGE: "Instance teardown (wasm-client-benchmarks) finished with status: ${{ job.status }}. (${{ env.ACTION_RUN_URL }})"
