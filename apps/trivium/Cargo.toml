[package]
name = "tfhe-trivium"
version = "0.1.0"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
rayon = "1"
tfhe = { path = "../../tfhe", features = ["boolean", "shortint", "integer"] }

[dev-dependencies]
criterion = { version = "0.5.1", features = ["html_reports"] }

[profile.devo]
inherits = "dev"
opt-level = 3
lto = "off"
debug-assertions = false

[[bench]]
name = "trivium"
harness = false
